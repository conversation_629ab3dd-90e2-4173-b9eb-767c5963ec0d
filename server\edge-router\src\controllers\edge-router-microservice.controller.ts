import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { IntelligentRoutingService } from '../services/intelligent-routing.service';
import { EdgeRegistryClientService } from '../services/edge-registry-client.service';
import { RoutingDecisionRequestDto } from '../dto/routing-request.dto';

/**
 * 边缘路由微服务控制器
 * 处理来自其他微服务的消息
 */
@Controller()
export class EdgeRouterMicroserviceController {
  private readonly logger = new Logger(EdgeRouterMicroserviceController.name);

  constructor(
    private readonly routingService: IntelligentRoutingService,
    private readonly edgeRegistryClient: EdgeRegistryClientService,
  ) {}

  /**
   * 处理路由决策消息
   */
  @MessagePattern('router.decide')
  async handleRoutingDecision(@Payload() data: RoutingDecisionRequestDto) {
    try {
      this.logger.log(`收到路由决策消息: 客户端 ${data.clientInfo.clientId}`);
      
      const decision = await this.routingService.makeRoutingDecision(data);
      
      return {
        success: !!decision,
        data: decision,
        message: decision ? '路由决策成功' : '没有可用节点',
      };
    } catch (error) {
      this.logger.error(`处理路由决策消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '路由决策失败',
      };
    }
  }

  /**
   * 处理获取可用节点消息
   */
  @MessagePattern('router.get-nodes')
  async handleGetNodes(@Payload() data: { region?: string }) {
    try {
      this.logger.log(`收到获取节点消息: 区域 ${data.region || '全部'}`);
      
      const nodes = await this.edgeRegistryClient.getAllNodes(data.region);
      
      return {
        success: true,
        data: nodes,
        message: '获取节点列表成功',
      };
    } catch (error) {
      this.logger.error(`处理获取节点消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '获取节点列表失败',
      };
    }
  }

  /**
   * 处理网络质量测量消息
   */
  @MessagePattern('router.network-quality')
  async handleNetworkQuality(@Payload() data: { nodeId: string; clientIp?: string }) {
    try {
      this.logger.log(`收到网络质量测量消息: 节点 ${data.nodeId}`);
      
      const quality = await this.routingService.measureNetworkQuality(data.nodeId, data.clientIp || '127.0.0.1');
      
      return {
        success: !!quality,
        data: quality,
        message: quality ? '网络质量测量成功' : '节点不存在或测量失败',
      };
    } catch (error) {
      this.logger.error(`处理网络质量测量消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '网络质量测量失败',
      };
    }
  }

  /**
   * 处理获取路由统计消息
   */
  @MessagePattern('router.get-stats')
  async handleGetStats() {
    try {
      this.logger.log('收到获取路由统计消息');
      
      const stats = await this.routingService.getRoutingStats();
      
      return {
        success: true,
        data: stats,
        message: '获取路由统计成功',
      };
    } catch (error) {
      this.logger.error(`处理获取路由统计消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '获取路由统计失败',
      };
    }
  }

  /**
   * 处理获取可用区域消息
   */
  @MessagePattern('router.get-regions')
  async handleGetRegions() {
    try {
      this.logger.log('收到获取可用区域消息');
      
      const regions = await this.edgeRegistryClient.getAvailableRegions();
      
      return {
        success: true,
        data: regions,
        message: '获取可用区域成功',
      };
    } catch (error) {
      this.logger.error(`处理获取可用区域消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '获取可用区域失败',
      };
    }
  }

  /**
   * 处理健康检查消息
   */
  @MessagePattern('router.health-check')
  async handleHealthCheck() {
    try {
      // 检查边缘注册中心连接状态
      const registryHealthy = await this.edgeRegistryClient.checkHealth();
      
      return {
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          edgeRegistryConnected: registryHealthy,
        },
        message: '边缘路由服务健康',
      };
    } catch (error) {
      this.logger.error(`处理健康检查消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '边缘路由服务不健康',
      };
    }
  }

  /**
   * 处理清空缓存消息
   */
  @MessagePattern('router.clear-cache')
  async handleClearCache() {
    try {
      this.logger.log('收到清空缓存消息');
      
      // 这里应该调用缓存服务的清空方法
      // await this.cacheService.clear();
      
      return {
        success: true,
        message: '缓存清空成功',
      };
    } catch (error) {
      this.logger.error(`处理清空缓存消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '清空缓存失败',
      };
    }
  }

  /**
   * 处理批量路由决策消息
   */
  @MessagePattern('router.batch-decide')
  async handleBatchRoutingDecision(@Payload() data: { requests: RoutingDecisionRequestDto[] }) {
    try {
      this.logger.log(`收到批量路由决策消息: ${data.requests.length} 个请求`);
      
      const decisions = await Promise.all(
        data.requests.map(request => 
          this.routingService.makeRoutingDecision(request)
        )
      );
      
      return {
        success: true,
        data: decisions,
        message: '批量路由决策成功',
      };
    } catch (error) {
      this.logger.error(`处理批量路由决策消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '批量路由决策失败',
      };
    }
  }
}
