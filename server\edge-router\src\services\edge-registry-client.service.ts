import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientProxy, ClientProxyFactory, Transport } from '@nestjs/microservices';
import axios from 'axios';

/**
 * 边缘节点信息接口
 */
export interface EdgeNode {
  nodeId: string;
  region: string;
  endpoint: string;
  status: string;
  capabilities: {
    maxUsers: number;
    supportedFeatures: string[];
    resources: {
      cpu: string;
      memory: string;
      storage: string;
    };
  };
  metrics: {
    currentUsers: number;
    cpuUsage: number;
    memoryUsage: number;
    networkLatency: number;
    uptime: number;
  };
  location: {
    latitude: number;
    longitude: number;
    city: string;
    country: string;
  };
  lastHeartbeat: Date;
  registeredAt: Date;
  version: string;
  metadata?: Record<string, any>;
}

/**
 * 边缘注册中心客户端服务
 * 负责与边缘注册中心通信，获取边缘节点信息
 */
@Injectable()
export class EdgeRegistryClientService implements OnModuleInit {
  private readonly logger = new Logger(EdgeRegistryClientService.name);
  private edgeRegistryClient: ClientProxy;
  private edgeRegistryHttpUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.edgeRegistryHttpUrl = `http://${this.configService.get<string>('EDGE_REGISTRY_HOST', 'localhost')}:${this.configService.get<number>('EDGE_REGISTRY_PORT', 3011)}/api`;
  }

  async onModuleInit() {
    this.logger.log('边缘注册中心客户端服务初始化');
    this.initMicroserviceClient();
  }

  /**
   * 初始化微服务客户端
   */
  private initMicroserviceClient(): void {
    this.edgeRegistryClient = ClientProxyFactory.create({
      transport: Transport.TCP,
      options: {
        host: this.configService.get<string>('EDGE_REGISTRY_HOST', 'localhost'),
        port: this.configService.get<number>('EDGE_REGISTRY_PORT', 3011),
      },
    });
  }

  /**
   * 获取所有可用的边缘节点
   */
  async getAllNodes(region?: string): Promise<EdgeNode[]> {
    try {
      // 优先使用微服务通信
      const result = await this.edgeRegistryClient
        .send('edge.get-all-nodes', { region })
        .toPromise();

      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      this.logger.warn(`微服务获取节点失败，尝试HTTP请求: ${error.message}`);
      
      // 降级到HTTP请求
      return this.getAllNodesHttp(region);
    }
  }

  /**
   * 通过HTTP获取所有节点
   */
  private async getAllNodesHttp(region?: string): Promise<EdgeNode[]> {
    try {
      const url = region 
        ? `${this.edgeRegistryHttpUrl}/edge/nodes?region=${region}`
        : `${this.edgeRegistryHttpUrl}/edge/nodes`;

      const response = await axios.get(url, {
        timeout: 5000,
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      this.logger.error(`HTTP获取节点失败: ${error.message}`);
      throw new Error('无法获取边缘节点信息');
    }
  }

  /**
   * 获取单个边缘节点信息
   */
  async getNode(nodeId: string): Promise<EdgeNode | null> {
    try {
      // 优先使用微服务通信
      const result = await this.edgeRegistryClient
        .send('edge.get-node', { nodeId })
        .toPromise();

      if (result.success) {
        return result.data;
      } else {
        return null;
      }
    } catch (error) {
      this.logger.warn(`微服务获取单个节点失败，尝试HTTP请求: ${error.message}`);
      
      // 降级到HTTP请求
      return this.getNodeHttp(nodeId);
    }
  }

  /**
   * 通过HTTP获取单个节点
   */
  private async getNodeHttp(nodeId: string): Promise<EdgeNode | null> {
    try {
      const response = await axios.get(`${this.edgeRegistryHttpUrl}/edge/nodes/${nodeId}`, {
        timeout: 5000,
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        return null;
      }
    } catch (error) {
      this.logger.error(`HTTP获取单个节点失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取集群统计信息
   */
  async getClusterStats(): Promise<{
    totalNodes: number;
    onlineNodes: number;
    offlineNodes: number;
    overloadedNodes: number;
    totalUsers: number;
    averageCpuUsage: number;
    averageMemoryUsage: number;
    availableRegions: string[];
  } | null> {
    try {
      // 优先使用微服务通信
      const result = await this.edgeRegistryClient
        .send('edge.get-stats', {})
        .toPromise();

      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      this.logger.warn(`微服务获取集群统计失败，尝试HTTP请求: ${error.message}`);
      
      // 降级到HTTP请求
      return this.getClusterStatsHttp();
    }
  }

  /**
   * 通过HTTP获取集群统计
   */
  private async getClusterStatsHttp(): Promise<any> {
    try {
      const response = await axios.get(`${this.edgeRegistryHttpUrl}/edge/stats`, {
        timeout: 5000,
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      this.logger.error(`HTTP获取集群统计失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 选择最优节点
   */
  async selectOptimalNode(criteria: {
    strategy?: string;
    region?: string;
    clientLocation?: {
      latitude: number;
      longitude: number;
    };
    minResources?: {
      cpu?: string;
      memory?: string;
      storage?: string;
    };
    requiredFeatures?: string[];
  }): Promise<EdgeNode | null> {
    try {
      // 优先使用微服务通信
      const result = await this.edgeRegistryClient
        .send('edge.select-optimal', criteria)
        .toPromise();

      if (result.success) {
        return result.data;
      } else {
        return null;
      }
    } catch (error) {
      this.logger.warn(`微服务选择最优节点失败，尝试HTTP请求: ${error.message}`);
      
      // 降级到HTTP请求
      return this.selectOptimalNodeHttp(criteria);
    }
  }

  /**
   * 通过HTTP选择最优节点
   */
  private async selectOptimalNodeHttp(criteria: any): Promise<EdgeNode | null> {
    try {
      const response = await axios.post(`${this.edgeRegistryHttpUrl}/edge/select-optimal`, criteria, {
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        return null;
      }
    } catch (error) {
      this.logger.error(`HTTP选择最优节点失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 检查边缘注册中心健康状态
   */
  async checkHealth(): Promise<boolean> {
    try {
      // 优先使用微服务通信
      const result = await this.edgeRegistryClient
        .send('edge.health-check', {})
        .toPromise();

      return result.success;
    } catch (error) {
      this.logger.warn(`微服务健康检查失败，尝试HTTP请求: ${error.message}`);
      
      // 降级到HTTP请求
      return this.checkHealthHttp();
    }
  }

  /**
   * 通过HTTP检查健康状态
   */
  private async checkHealthHttp(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.edgeRegistryHttpUrl}/health`, {
        timeout: 3000,
      });

      return response.status === 200;
    } catch (error) {
      this.logger.error(`HTTP健康检查失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取单个边缘节点信息（别名方法）
   */
  async getNodeById(nodeId: string): Promise<EdgeNode | null> {
    return this.getNode(nodeId);
  }

  /**
   * 获取可用区域列表
   */
  async getAvailableRegions(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.edgeRegistryHttpUrl}/edge/regions`, {
        timeout: 5000,
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        return [];
      }
    } catch (error) {
      this.logger.error(`获取可用区域失败: ${error.message}`);
      return [];
    }
  }
}
