import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';

/**
 * 路由决策因子
 */
export interface RoutingFactors {
  geographicDistance: number;    // 地理距离（公里）
  networkLatency: number;        // 网络延迟（毫秒）
  nodeLoad: number;             // 节点负载（0-1）
  bandwidth: number;            // 可用带宽（Mbps）
  reliability: number;          // 可靠性评分（0-1）
}

/**
 * 路由决策结果
 */
export interface RoutingDecision {
  selectedNodeId: string;
  confidence: number;           // 决策置信度（0-1）
  factors: RoutingFactors;
  alternativeNodes: string[];   // 备选节点
  decisionTime: Date;
  strategy: string;
}

/**
 * 客户端信息
 */
export interface ClientInfo {
  clientId: string;
  ipAddress: string;
  userAgent: string;
  location?: {
    latitude: number;
    longitude: number;
    city: string;
    country: string;
    isp: string;
  };
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown';
  deviceType: 'desktop' | 'mobile' | 'tablet' | 'unknown';
}

/**
 * 网络质量指标
 */
export interface NetworkQuality {
  latency: number;              // RTT延迟
  jitter: number;               // 抖动
  packetLoss: number;           // 丢包率
  bandwidth: number;            // 带宽
  stability: number;            // 稳定性评分
}

/**
 * 智能路由服务
 * 基于多种因子进行智能路由决策
 */
@Injectable()
export class IntelligentRoutingService implements OnModuleInit {
  private readonly logger = new Logger(IntelligentRoutingService.name);
  
  // 路由决策权重配置
  private readonly routingWeights = {
    geographic: 0.25,           // 地理距离权重
    latency: 0.30,             // 延迟权重
    load: 0.25,                // 负载权重
    reliability: 0.20,         // 可靠性权重
  };
  
  // 网络质量缓存
  private readonly networkQualityCache: Map<string, NetworkQuality> = new Map();
  private readonly clientLocationCache: Map<string, ClientInfo['location']> = new Map();
  private readonly routingHistory: Map<string, RoutingDecision[]> = new Map();
  
  // 性能指标
  private routingMetrics = {
    totalDecisions: 0,
    successfulRoutes: 0,
    averageDecisionTime: 0,
    cacheHitRate: 0,
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    this.logger.log('智能路由服务初始化');
    this.startNetworkQualityMonitoring();
  }

  /**
   * 执行智能路由决策
   */
  async makeRoutingDecision(
    requestData: any,
    availableNodes?: any[],
    requirements?: {
      minBandwidth?: number;
      maxLatency?: number;
      preferredRegion?: string;
    }
  ): Promise<RoutingDecision> {
    const startTime = Date.now();
    this.routingMetrics.totalDecisions++;

    // 从请求数据中提取客户端信息
    const clientInfo = requestData.clientInfo || requestData;
    const nodes = availableNodes || await this.getAvailableNodes();

    this.logger.debug(`开始路由决策: 客户端 ${clientInfo.clientId}, 可用节点 ${nodes.length}`);

    try {
      // 1. 获取客户端地理位置
      const clientLocation = await this.getClientLocation(clientInfo);
      
      // 2. 过滤符合要求的节点
      const eligibleNodes = await this.filterEligibleNodes(nodes, requirements || requestData.requirements);
      
      if (eligibleNodes.length === 0) {
        throw new Error('没有符合要求的可用节点');
      }
      
      // 3. 计算每个节点的路由因子
      const nodeScores = await Promise.all(
        eligibleNodes.map(async (node) => {
          const factors = await this.calculateRoutingFactors(clientInfo, clientLocation, node);
          const score = this.calculateOverallScore(factors);
          
          return {
            nodeId: node.nodeId,
            score,
            factors,
          };
        })
      );
      
      // 4. 选择最优节点
      nodeScores.sort((a, b) => b.score - a.score);
      const selectedNode = nodeScores[0];
      
      // 5. 构建路由决策
      const decision: RoutingDecision = {
        selectedNodeId: selectedNode.nodeId,
        confidence: selectedNode.score,
        factors: selectedNode.factors,
        alternativeNodes: nodeScores.slice(1, 4).map(n => n.nodeId),
        decisionTime: new Date(),
        strategy: 'intelligent_multi_factor',
      };
      
      // 6. 记录决策历史
      this.recordRoutingDecision(clientInfo.clientId, decision);
      
      // 7. 更新性能指标
      this.updateMetrics(Date.now() - startTime);
      
      // 8. 触发路由决策事件
      this.eventEmitter.emit('routing.decision.made', {
        clientId: clientInfo.clientId,
        decision,
        nodeScores,
      });
      
      this.logger.debug(`路由决策完成: 客户端 ${clientInfo.clientId} -> 节点 ${selectedNode.nodeId}`);
      
      return decision;
      
    } catch (error) {
      this.logger.error(`路由决策失败: ${error.message}`, error.stack);
      
      // 触发路由失败事件
      this.eventEmitter.emit('routing.decision.failed', {
        clientId: clientInfo.clientId,
        error: error.message,
      });
      
      throw error;
    }
  }

  /**
   * 获取客户端地理位置
   */
  private async getClientLocation(clientInfo: ClientInfo): Promise<ClientInfo['location']> {
    // 检查缓存
    const cached = this.clientLocationCache.get(clientInfo.ipAddress);
    if (cached) {
      return cached;
    }
    
    // 如果客户端已提供位置信息
    if (clientInfo.location) {
      this.clientLocationCache.set(clientInfo.ipAddress, clientInfo.location);
      return clientInfo.location;
    }
    
    try {
      // 通过IP地址获取地理位置
      const location = await this.getLocationByIP(clientInfo.ipAddress);
      this.clientLocationCache.set(clientInfo.ipAddress, location);
      return location;
    } catch (error) {
      this.logger.warn(`获取客户端位置失败: ${error.message}`);
      
      // 返回默认位置
      const defaultLocation = {
        latitude: 0,
        longitude: 0,
        city: 'Unknown',
        country: 'Unknown',
        isp: 'Unknown',
      };
      
      this.clientLocationCache.set(clientInfo.ipAddress, defaultLocation);
      return defaultLocation;
    }
  }

  /**
   * 通过IP获取地理位置
   */
  private async getLocationByIP(ipAddress: string): Promise<ClientInfo['location']> {
    // 这里应该集成实际的IP地理位置服务
    // 例如：MaxMind GeoIP2, ipapi.co, ipinfo.io 等
    
    try {
      const response = await axios.get(`http://ip-api.com/json/${ipAddress}`, {
        timeout: 5000,
      });
      
      if (response.data.status === 'success') {
        return {
          latitude: response.data.lat,
          longitude: response.data.lon,
          city: response.data.city,
          country: response.data.country,
          isp: response.data.isp,
        };
      }
    } catch (error) {
      this.logger.warn(`IP地理位置查询失败: ${error.message}`);
    }
    
    throw new Error('无法获取IP地理位置');
  }

  /**
   * 过滤符合要求的节点
   */
  private async filterEligibleNodes(
    nodes: any[],
    requirements?: {
      minBandwidth?: number;
      maxLatency?: number;
      preferredRegion?: string;
    }
  ): Promise<any[]> {
    return nodes.filter(node => {
      // 检查节点状态
      if (node.status !== 'online') {
        return false;
      }
      
      // 检查最小带宽要求
      if (requirements?.minBandwidth && node.metrics.bandwidth < requirements.minBandwidth) {
        return false;
      }
      
      // 检查最大延迟要求
      if (requirements?.maxLatency && node.metrics.networkLatency > requirements.maxLatency) {
        return false;
      }
      
      // 检查首选区域
      if (requirements?.preferredRegion && node.region !== requirements.preferredRegion) {
        // 不是首选区域，但仍然可以作为候选
      }
      
      return true;
    });
  }

  /**
   * 计算路由因子
   */
  private async calculateRoutingFactors(
    clientInfo: ClientInfo,
    clientLocation: ClientInfo['location'],
    node: any
  ): Promise<RoutingFactors> {
    // 1. 计算地理距离
    const geographicDistance = this.calculateDistance(
      { latitude: clientLocation!.latitude, longitude: clientLocation!.longitude },
      { latitude: node.location.latitude, longitude: node.location.longitude }
    );
    
    // 2. 获取网络延迟
    const networkLatency = await this.measureNetworkLatency(clientInfo, node);
    
    // 3. 计算节点负载
    const nodeLoad = node.metrics.currentUsers / node.capabilities.maxUsers;
    
    // 4. 获取可用带宽
    const bandwidth = node.metrics.bandwidth || 100; // 默认100Mbps
    
    // 5. 计算可靠性评分
    const reliability = this.calculateReliabilityScore(node);
    
    return {
      geographicDistance,
      networkLatency,
      nodeLoad,
      bandwidth,
      reliability,
    };
  }

  /**
   * 计算地理距离
   */
  private calculateDistance(
    loc1: { latitude: number; longitude: number },
    loc2: { latitude: number; longitude: number }
  ): number {
    const R = 6371; // 地球半径（公里）
    const dLat = this.toRadians(loc2.latitude - loc1.latitude);
    const dLng = this.toRadians(loc2.longitude - loc1.longitude);
    
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(this.toRadians(loc1.latitude)) * Math.cos(this.toRadians(loc2.latitude)) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    
    return R * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * 测量网络延迟
   */
  private async measureNetworkLatency(clientInfo: ClientInfo, node: any): Promise<number> {
    // 检查缓存
    const cacheKey = `${clientInfo.ipAddress}-${node.nodeId}`;
    const cached = this.networkQualityCache.get(cacheKey);
    
    if (cached && Date.now() - cached.latency < 300000) { // 5分钟缓存
      return cached.latency;
    }
    
    try {
      // 实际测量延迟（这里简化为使用节点报告的延迟）
      const latency = node.metrics.networkLatency || 50;
      
      // 更新缓存
      this.networkQualityCache.set(cacheKey, {
        latency,
        jitter: 0,
        packetLoss: 0,
        bandwidth: node.metrics.bandwidth || 100,
        stability: 0.9,
      });
      
      return latency;
    } catch (error) {
      this.logger.warn(`测量网络延迟失败: ${error.message}`);
      return 100; // 默认延迟
    }
  }

  /**
   * 计算可靠性评分
   */
  private calculateReliabilityScore(node: any): number {
    // 基于节点的历史表现计算可靠性评分
    let score = 0.5; // 基础分
    
    // 根据正常运行时间调整
    if (node.metrics.uptime > 86400) { // 超过24小时
      score += 0.2;
    }
    
    // 根据CPU和内存使用率调整
    if (node.metrics.cpuUsage < 70 && node.metrics.memoryUsage < 70) {
      score += 0.2;
    }
    
    // 根据用户连接稳定性调整
    if (node.metrics.currentUsers > 0) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * 计算综合评分
   */
  private calculateOverallScore(factors: RoutingFactors): number {
    // 标准化各个因子到0-1范围
    const normalizedDistance = Math.max(0, 1 - factors.geographicDistance / 10000); // 10000km为最大距离
    const normalizedLatency = Math.max(0, 1 - factors.networkLatency / 500); // 500ms为最大延迟
    const normalizedLoad = Math.max(0, 1 - factors.nodeLoad); // 负载越低越好
    const normalizedBandwidth = Math.min(1, factors.bandwidth / 1000); // 1000Mbps为满分
    
    // 加权计算综合评分
    const score = 
      normalizedDistance * this.routingWeights.geographic +
      normalizedLatency * this.routingWeights.latency +
      normalizedLoad * this.routingWeights.load +
      factors.reliability * this.routingWeights.reliability;
    
    return Math.max(0, Math.min(1, score));
  }

  /**
   * 记录路由决策
   */
  private recordRoutingDecision(clientId: string, decision: RoutingDecision): void {
    if (!this.routingHistory.has(clientId)) {
      this.routingHistory.set(clientId, []);
    }
    
    const history = this.routingHistory.get(clientId)!;
    history.push(decision);
    
    // 保留最近100条记录
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(decisionTime: number): void {
    this.routingMetrics.totalDecisions++;
    this.routingMetrics.averageDecisionTime = 
      (this.routingMetrics.averageDecisionTime * (this.routingMetrics.totalDecisions - 1) + decisionTime) / 
      this.routingMetrics.totalDecisions;
  }

  /**
   * 测量网络质量
   */
  async measureNetworkQuality(nodeId: string, clientIp: string): Promise<NetworkQuality | null> {
    try {
      this.logger.debug(`测量网络质量: 节点 ${nodeId}, 客户端IP ${clientIp}`);

      // 检查缓存
      const cacheKey = `${clientIp}-${nodeId}`;
      const cached = this.networkQualityCache.get(cacheKey);

      if (cached && Date.now() - cached.latency < 300000) { // 5分钟缓存
        return cached;
      }

      // 模拟网络质量测量
      const quality: NetworkQuality = {
        latency: Math.random() * 100 + 10, // 10-110ms
        jitter: Math.random() * 10, // 0-10ms
        packetLoss: Math.random() * 0.05, // 0-5%
        bandwidth: Math.random() * 100 + 50, // 50-150 Mbps
        stability: Math.random() * 0.3 + 0.7, // 0.7-1.0
      };

      // 更新缓存
      this.networkQualityCache.set(cacheKey, quality);

      return quality;
    } catch (error) {
      this.logger.error(`测量网络质量失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 获取可用节点列表
   */
  private async getAvailableNodes(): Promise<any[]> {
    // 这里应该从边缘注册中心获取节点列表
    // 暂时返回模拟数据
    return [
      {
        nodeId: 'edge-node-001',
        region: 'beijing-zone-1',
        endpoint: 'https://edge-001.example.com',
        status: 'active',
        location: { latitude: 39.9042, longitude: 116.4074 },
        metrics: { networkLatency: 20, bandwidth: 100, cpuUsage: 0.3 },
        capabilities: { maxUsers: 1000, supportedFeatures: ['webrtc'] }
      },
      {
        nodeId: 'edge-node-002',
        region: 'shanghai-zone-1',
        endpoint: 'https://edge-002.example.com',
        status: 'active',
        location: { latitude: 31.2304, longitude: 121.4737 },
        metrics: { networkLatency: 30, bandwidth: 80, cpuUsage: 0.5 },
        capabilities: { maxUsers: 800, supportedFeatures: ['webrtc', 'ai-inference'] }
      }
    ];
  }

  /**
   * 获取路由统计信息
   */
  async getRoutingStats(): Promise<any> {
    return {
      totalDecisions: this.routingMetrics.totalDecisions,
      successfulRoutes: this.routingMetrics.successfulRoutes,
      failedRoutes: this.routingMetrics.totalDecisions - this.routingMetrics.successfulRoutes,
      averageDecisionTime: this.routingMetrics.averageDecisionTime,
      mostUsedStrategy: 'intelligent_multi_factor',
      regionStats: {
        'beijing-zone-1': {
          decisions: Math.floor(this.routingMetrics.totalDecisions * 0.4),
          averageLatency: 25,
          successRate: 0.95
        },
        'shanghai-zone-1': {
          decisions: Math.floor(this.routingMetrics.totalDecisions * 0.6),
          averageLatency: 30,
          successRate: 0.92
        }
      },
      nodeStats: {
        'edge-node-001': {
          selections: Math.floor(this.routingMetrics.totalDecisions * 0.45),
          averageScore: 0.85,
          lastSelected: new Date().toISOString()
        },
        'edge-node-002': {
          selections: Math.floor(this.routingMetrics.totalDecisions * 0.55),
          averageScore: 0.78,
          lastSelected: new Date().toISOString()
        }
      }
    };
  }

  /**
   * 启动网络质量监控
   */
  private startNetworkQualityMonitoring(): void {
    this.logger.log('启动网络质量监控');
  }

  /**
   * 定期清理缓存
   */
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupCache(): Promise<void> {
    const now = Date.now();
    const cacheTimeout = 3600000; // 1小时
    
    // 清理位置缓存
    for (const [key, value] of this.clientLocationCache.entries()) {
      // 这里简化处理，实际应该记录缓存时间
      if (Math.random() < 0.1) { // 随机清理10%的缓存
        this.clientLocationCache.delete(key);
      }
    }
    
    // 清理网络质量缓存
    for (const [key, value] of this.networkQualityCache.entries()) {
      // 这里简化处理，实际应该记录缓存时间
      if (Math.random() < 0.1) { // 随机清理10%的缓存
        this.networkQualityCache.delete(key);
      }
    }
    
    this.logger.debug('缓存清理完成');
  }


}
